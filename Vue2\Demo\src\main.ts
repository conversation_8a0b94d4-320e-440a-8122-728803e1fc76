import {
  StoreName,
  TUIStore,
  TUITranslateService,
} from "@tencentcloud/chat-uikit-engine"
import {
  Button,
  Checkbox,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Form,
  FormItem,
  Input,
} from "element-ui"
import "element-ui/lib/theme-chalk/index.css"
import Vue from "vue"
import App from "./App.vue"
import { locales } from "./locales"
import router from "./router"
import store from "./store"
import { TUIChatKit, TUIComponents } from "./TUIKit"
import TUINotification from "./TUIKit/components/TUINotification/index"

const SDKAppID = 1600074225 // Your SDKAppID
const secretKey =
  "34fff519786a874ec8b23dc62d0bfbba81403f4d864f8cd841561e352069769c" // Your secretKey

TUIChatKit.components(TUIComponents, Vue)
TUIChatKit.init()

TUITranslateService.provideLanguages(locales)
TUITranslateService.useI18n()

Vue.config.productionTip = false
Vue.use(Button)
Vue.use(Form)
Vue.use(FormItem)
Vue.use(Dropdown)
Vue.use(DropdownMenu)
Vue.use(DropdownItem)
Vue.use(Input)
Vue.use(Checkbox)

new Vue({
  router,
  store,
  render: h => h(App),
}).$mount("#app")

/**
 * Init TUINotification configuration.
 */
TUINotification.setNotificationConfiguration({
  showPreviews: true,
  allowNotifications: true,
  notificationTitle: "Tencent Cloud Chat",
  notificationIcon: "https://web.sdk.qcloud.com/im/demo/latest/faviconnew.png",
})

/**
 * Listen for new messages and use notification components.
 * This capability is only available in the web environmen.
 */
TUIStore.watch(StoreName.CHAT, {
  newMessageList: (newMessageList: unknown) => {
    if (Array.isArray(newMessageList)) {
      newMessageList.forEach(message => TUINotification.notify(message))
    }
  },
})

export { SDKAppID, secretKey }
