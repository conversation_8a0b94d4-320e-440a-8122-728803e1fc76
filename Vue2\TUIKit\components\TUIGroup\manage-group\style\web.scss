.btn {
  padding: 4px 28px;
  font-size: 12px;
  line-height: 24px;
  border-radius: 4px;
}

.list {
  padding: 0 20px;
  display: flex;
  flex-direction: column;

  &-item {
    padding: 14px 0;
    display: flex;
    align-items: center;
    font-size: 14px;
  }

  &-between {
    justify-content: space-between;
  }
}

.manage {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;

  &-header {
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-content {
      margin-left: -20px;
      text-align: center;
      font-size: 16px;
      line-height: 30px;
      font-weight: 500;
    }

    &-left {
      display: flex;
    }
  }

  .main {
    .user-info {
      padding: 0 20px;
      display: flex;
      flex-direction: column;
      font-size: 14px;

      &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 14px 0;

        &-right {
          display: flex;
          align-items: center;
        }
      }

      &-list {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 20px;

        &-item {
          position: relative;
          flex: 0 0 36px;
          display: flex;
          flex-direction: column;
          padding-right: 20px;

          &:last-child {
            padding-right: 0;
          }

          .more {
            padding-top: 10px;
          }

          &-info {
            text-align: center;
            max-width: 36px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .content {
      padding: 0 20px;

      .list-item {
        justify-content: space-between;

        .btn {
          flex: 1;
        }

        .article {
          opacity: 0.6;
          width: 246px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .end {
          align-self: flex-end;
          margin-bottom: 4px;
        }
      }
    }

    .footer {
      padding: 0 20px;

      .list-item {
        cursor: pointer;
        width: 100%;
        font-size: 14px;
        padding: 14px 0;
        justify-content: center;

        &:last-child {
          border: none;
        }
      }
    }
  }

  .admin {
    padding: 20px 0;

    &-content {
      padding: 20px 20px 12px;
      display: flex;
      align-items: center;

      .aside {
        flex: 1;
        font-size: 14px;

        .p {
          font-size: 12px;
        }
      }
    }

    &-list {
      padding: 0 20px;

      .label {
        display: inline-block;
        font-size: 14px;
        padding-bottom: 8px;
      }
    }

    .last {
      padding-top: 13px;
      position: relative;

      &::before {
        position: absolute;
        content: "";
        width: calc(100% - 40px);
        height: 1px;
        top: 0;
        left: 0;
        right: 0;
        margin: 0 auto;
      }
    }
  }
}

.input {
  border-radius: 4px;
  padding: 4px 16px;
  font-size: 14px;
}

.group-id {
  display: flex;
  flex-direction: row;
  align-items: center;

  .icon {
    width: 15px;
    height: 15px;
    cursor: pointer;
  }
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slider {
  &-box {
    display: flex;
    align-items: center;
    width: 34px;
    height: 20px;
    border-radius: 10px;
  }

  &-block {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin: 0 2px;
  }
}

.space-between {
  justify-content: space-between;
}

.del-dialog-title {
  text-align: center;
  padding: 20px 0;
}
