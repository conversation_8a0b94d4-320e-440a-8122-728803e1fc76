#preloadedImages {
  background: linear-gradient(135deg, #f1f4f7 0%, #edf5ff 100%) no-repeat;
  background-size: cover;
  background-image: url("https://web.sdk.qcloud.com/im/assets/images/background-zip.png");
}

.home {
  box-sizing: border-box;
  flex: 1;
  display: flex;
  flex-direction: row;
  min-width: 1024px;
  background-size: contain;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .home-menu {
    box-sizing: border-box;
    display: flex;
  }
  .home-container {
    box-sizing: border-box;
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    align-items: stretch;
    .home-header {
      box-sizing: border-box;
      overflow: hidden;
      .header-menu-show {
        padding-left: 0px;
      }
    }
    .home-main {
      box-sizing: border-box;
      flex: 1;
      display: flex;
      justify-content: center;
      overflow: hidden;
      padding: 50px;
      min-width: 968px;
      .home-TUIKit {
        box-sizing: border-box;
        display: flex;
        flex: 1;
        width: 100%;
        height: 100%;
        max-width: 1400px;
        overflow: hidden;
        min-height: 640px;
        border-radius: 12px;
        background-color: #ffffff;
        box-shadow: 0 11px 20px 0 rgba(0, 0, 0, 0.3);
        .home-TUIKit-navbar {
          box-sizing: border-box;
          display: flex;
        }
        .home-TUIKit-main {
          box-sizing: border-box;
          flex: 1;
          display: flex;
          overflow: hidden;
          flex-direction: row;
          border: 0 solid black;
          .home-conversation,
          .home-relation {
            min-width: 285px;
            box-sizing: border-box;
            flex: 0 0 24%;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #f4f5f9;
          }
          .home-chat {
            box-sizing: border-box;
            min-width: 0;
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
          }
        }
        .callkit-drag-container {
          position: fixed;
          z-index: 100;
          background-color: #ffffff;
          user-select: none;
          &-pc {
            left: calc(50% - 25rem);
            top: calc(50% - 18rem);
            width: 50rem;
            height: 36rem;
            border-radius: 16px;
            box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
          }
          &-mini {
            width: 168px;
            height: 56px;
            right: 10px;
            top: 70px;
            background-color: transparent;
            border-radius: 0px;
            box-shadow: none;
          }
        }
      }
    }
  }
  .home-container.menu-expand {
    .dialog,
    .container {
      width: calc(100% - 300px) !important;
      left: 300px !important;
    }
    .home-main .home-TUIKit .callkit-drag-container.callkit-drag-container-pc {
      left: calc(50% - 25rem + 150px);
    }
  }
}