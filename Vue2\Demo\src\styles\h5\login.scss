@import "../common.scss";
.login-h5 {
  min-width: 100%;
  max-width: 100%;
  padding: 17px 0;
  .login-header {
    padding: 0 23px;
  }
  .login-main {
    .login-main-content {
      padding: 0 23px;
      background: url("../assets/image/h5/login-bg.png") no-repeat;
      background-size: 65%;
      background-position-x: right;
      align-items: flex-start;
      .login-form {
        flex: 1;
        .login-title {
          flex-direction: column;
          padding: 60px 0 18px;
          p {
            padding-left: 0;
            font-size: 27px;
            line-height: 40px;
          }
        }
        .login-form-item {
          font-size: 18px;
          .el-checkbox {
            .checked-text {
              font-size: 14px;
            }
          }
          .login-form-item-disabled {
            font-size: 18px;
            padding: 20px;
          }
        }
        .login-btn {
          button {
            height: auto !important;
            font-size: 20px;
            line-height: 27px;
            padding: 13px 0;
          }
        }
      }
    }
  }
  .login-footer {
    background: none;
    padding: 10px 10px;

    &-list {
      flex: 1;
      display: flex;

      &-item {
        flex: 1;
        display: flex;
        background: url("../assets/image/h5/adv-more.svg") no-repeat;
        background-size: 100% 100%;
        border: solid #96c3ff 1px;

        &:last-child {
          background: url("../assets/image/h5/adv-im.svg") no-repeat;
          background-size: 100% 100%;
        }

        a {
          flex: 1;
          display: flex;
          justify-content: space-around;
          align-items: center;
          box-sizing: border-box;
          padding: 20px;

          span {
            padding: 5px 20px;
            background: #147aff;
            box-shadow: 0 4px 5px 0 rgba(255, 255, 255, 0.7), 0 3px 8px 0 rgba(20, 122, 255, 0.55);
            border-radius: 30.5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 15px;
            color: #ffffff;
            letter-spacing: 0;
          }

          aside {
            display: flex;
            flex-direction: column;

            h1 {
              font-family: PingFangSC-Regular;
              font-size: 16px;
              color: #000000;
              letter-spacing: 0;
            }

            .sub {
              align-self: flex-end;
            }
          }
        }
      }

      &-bottom {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .text-header {
          display: flex;
          align-items: center;

          span {
            padding: 20px 20px;
            width: 84px;
            font-family: "PingFang SC";
            font-style: normal;
            font-weight: 400;
            color: #bbbbbb;
          }
        }

        i {
          width: 120px;
          height: 1px;
          background: #dbdbdb;
        }

        &-image {
          display: flex;

          .platform {
            width: 41px;
            height: 41px;
            padding: 0 20px;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}
