<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover"
    />
    <link rel="icon" href="<%= BASE_URL %>favicon.png" />
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
    <script>
      /**
       * Set the root node font size based on the base font size and the effect image width.
       * @param {number} baseFontSize - The default font size of the browser. [Note] The minimum font size for browsers is 12 pixels, so this value must be greater than or equal to 12.
       * @param {number} baseWidth - The dimensions of the design draft.
      */
      function initFontSize() {
        var clientWidth =
          document.documentElement.clientWidth || window.innerWidth;
        var size = Math.floor((clientWidth / 1920) * 16);
        size = size > 12 ? size : 12;
        document.querySelector("html").style.fontSize = size + "px";
      }
      initFontSize();
      // Set the root node font size when the window size changes, and control the scaling of page element sizes through the font size.
      window.addEventListener("resize", initFontSize);
      // Prevent page scaling
      window.onload = function () {
        var lastTouchEnd = 0;
        //Prevent double-click on scaling
        document.addEventListener("touchstart", function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        });
        document.addEventListener(
          "touchend",
          function (event) {
            var now = new Date().getTime();
            if (now - lastTouchEnd <= 300) {
              event.preventDefault();
            }
            lastTouchEnd = now;
          },
          false
        );
        // Prevent double fingers on scaling
        document.addEventListener("gesturestart", function (event) {
          event.preventDefault();
        });
        document.addEventListener("dblclick", function (event) {
          event.preventDefault();
        });
         // Prevent some Android browsers from natively forcing zoom-in.
        document.getElementById("app").addEventListener(
          "touchmove",
          function (e) {
            e.stopPropagation();
          },
          false
        );
      };
      // Handle the issue of Safari's bottom navigation bar overlapping content.
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", `${vh}px`);
      window.addEventListener("resize", () => {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty("--vh", `${vh}px`);
      });
    </script>
    <style>
      body {
        margin: 0;
        width: 100vw;
        max-height: 100vh;
      }
      @supports (-webkit-touch-callout: none) {
        body {
          min-height: -webkit-fill-available;
          max-height: -webkit-fill-available;
        }
      }
    </style>
    <!-- built files will be auto injected -->
  </body>
</html>
