.tui-conversation-list {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  letter-spacing: 0;
  flex: 1;
  overflow: auto;
}

.tui-conversation {
  &-item {
    padding: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
    box-sizing: border-box;
    overflow: hidden;

    .left {
      position: relative;
      width: 36px;
      height: 36px;

      .num {
        position: absolute;
        display: inline-block;
        right: 0;
        top: -5px;
        min-width: 10px;
        width: fit-content;
        padding: 0 2.5px;
        height: 15px;
        font-size: 10px;
        text-align: center;
        line-height: 15px;
        border-radius: 7.5px;
      }

      .num-notify {
        position: absolute;
        display: inline-block;
        right: 2px;
        top: -2px;
        width: 6px;
        height: 6px;
        font-size: 10px;
        text-align: center;
        line-height: 15px;
        border-radius: 65%;
      }

      .avatar {
        width: 30px;
        height: 30px;
        border-radius: 5px;
      }

      .online-status {
        box-sizing: border-box;
        position: absolute;
        width: 10px;
        height: 10px;
        left: 24px;
        top: 22px;
        border: 2px solid #fff;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
        border-radius: 50%;

        &-online {
          background: #29cc85;
        }

        &-offline {
          background: #a4a4a4;
        }
      }
    }

    .content-footer {
      line-height: 16px;
      display: flex;
      flex-direction: column;

      .time {
        font-size: 12px;
        line-height: 16px;
        display: inline-block;
        white-space: nowrap;
      }
    }

    .content {
      display: flex;
      flex: 1;
      padding-left: 8px;
      justify-content: space-between;
      box-sizing: border-box;
      overflow: hidden;

      .content-footer {
        align-items: flex-end;

        .icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin: 0;
        }
      }
    }

    .content-header {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      box-sizing: border-box;

      &-label {
        flex: 1;
        font-size: 14px;
      }

      .name {
        width: 110px;
        letter-spacing: 0;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .middle-box {
        flex: 1;
        display: flex;
        align-items: center;

        &-at,
        &-draft {
          font-size: 12px;
        }

        &-content {
          flex: 1;
          width: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 12px;
          line-height: 16px;
        }
      }
    }

    &-content {
      position: relative;

      .tui-conversation-item:hover {
        background: rgba(0, 110, 255, 0.1);
      }

      .dialog {
        position: absolute;
        z-index: 5;
        padding: 2px 20px;
        cursor: pointer;

        &-item {
          top: 30px;
          left: 164px;
          border-radius: 8px;
        }

        .conversation-options {
          padding: 5px 0;
          height: 17px;
          font-size: 12px;
          line-height: 17px;
        }

        &-item-up {
          top: -50px;
        }
      }
    }
  }
}
