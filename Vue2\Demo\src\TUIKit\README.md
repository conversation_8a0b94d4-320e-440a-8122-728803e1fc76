<h1 align="center">chat-uikit-vue</h1>
<p align="center">
  Globally interconnected In-App Chat, user profile and relationship chains and online/offline push.
</p>
<p align="center">
  <span>English / </span>
  <a href="https://github.com/TencentCloud/chat-uikit-vue/blob/main/.github/README_ZH.md">简体中文</a>
</p>

![image](https://github.com/TencentCloud/chat-uikit-vue/assets/57951148/7bd24604-1e5e-4541-8992-245dccbbc810)
![image](https://github.com/TencentCloud/chat-uikit-vue/assets/57951148/40ae2f49-39ae-432d-8d1b-5b46414bc3b4)

## About chat-uikit-vue

[chat-uikit-vue](https://www.npmjs.com/package/@tencentcloud/chat-uikit-vue) is a Vue UI component library based on Tencent Cloud Chat SDK. It provides universally used UI components that include Conversation, Chat, and Group components. Leveraging these meticulously crafted UI components, you can quickly construct an elegant, reliable, and scalable Chat application.

> In respect for the copyright of the emoji design, the Chat Demo/TUIKit project does not include the cutouts of large emoji elements. Please replace them with your own designed or copyrighted emoji packs before the official launch for commercial use. The default small yellow face emoji pack is copyrighted by Tencent Cloud and can be authorized for a fee. If you wish to obtain authorization, please [submit a ticket](https://console.tencentcloud.com/workorder/category?level1_id=29&level2_id=40&source=14&data_title=Chat&step=1) to contact us.


## Core Capabilities

- UIKit: Build a fully-featured chat app in just ten minutes using UIKit components
- Various Message Types: Support multiple message types, such as text, images, audio, and video messages
- Notifications: Timely notify users of app messages and drive higher conversion rates
- Group Management: Create groups, manage group members, and customize profiles
- Conversation Management: View information about unread messages, recent, pinned, or deleted chats

## Demo

👉🏻 [Free Demo](https://web.sdk.qcloud.com/im/demo/intl/index.html?scene=social)

## Send Your First Message

### Vue3
- [Integration TUIKit](https://trtc.io/document/58644?platform=web&product=chat)
- [Quick Run Demo](https://github.com/TencentCloud/chat-uikit-vue/blob/main/Vue3/Demo/README.md)

### Vue2
- [Integration TUIKit](https://trtc.io/document/58644?platform=web&product=chat)
- [Quick Run Demo](https://github.com/TencentCloud/chat-uikit-vue/blob/main/Vue2/Demo/README.md)


## TUILogin 

``` javascript
import { TUILogin } from '@tencentcloud/tui-core';
```
The options parameter is of the Object type. It contains the following attribute values:
| Name | Type | Description |
| --- | --- | --- |
| SDKAppID | number | Required, SDKAppID of the chat app |
| userID | string | Required, user ID|
| userSig |string | Required, the password with which the user logs in to the Chat console. It is essentially the ciphertext generated by encrypting information such as the UserID.For the detailed generation method, see [Generating UserSig](https://trtc.io/document/34385) |
| TIMPush | any | Optional, TIMPush plugin instance when uniapp build app packages |
| pushConfig | object | Optional, TIMPush plugin's config |
| useUploadPlugin | boolean | Optional, whether to use the upload plugin, the default is false |
| proxyServer | string | Optional, WebSocket server proxy address |
| fileUploadProxy | string | Optional, image, video, file upload proxy address |
| fileDownloadProxy | string | Optional, image, video, file download proxy address|
| framework | string \| undefined | Required, UI framework type, optional values: vue2、vue3、undefined |

``` javascript
// Login
TUILogin.login(options);
```

``` javascript
// Logout
TUILogin.logout();
```

``` javascript
// Set the SDK log level.
// 0: Common level. You are advised to use this level during access as it covers more logs.
// 1: Release level. You are advised to use this level for key information in a production environment.
TUILogin.setLogLevel(0); 
```

``` javascript
// Get Chat SDK instance
const { chat } = TUILogin.getContext();
```

## Documentation
- [Home page](https://trtc.io/document/50061?platform=web&product=chat)
- [@tencentcloud/chat-uikit-vue npm](https://www.npmjs.com/package/@tencentcloud/chat-uikit-vue)
- [Chat SDK](https://trtc.io/document/34309?platform=web&product=chat)

## Contact Us
Join a Tencent Cloud Chat developer group for Reliable technical support & Product details & Constant exchange of ideas.
- Telegram group (EN): [join](https://t.me/+1doS9AUBmndhNGNl)
- WhatsApp group (EN): [join](https://chat.whatsapp.com/Gfbxk7rQBqc8Rz4pzzP27A)
- Telegram group (ZH): [join](https://t.me/tencent_imsdk)
- WhatsApp group (ZH): [join](https://chat.whatsapp.com/IVa11ZkVmKTEwSWsAzSyik)
