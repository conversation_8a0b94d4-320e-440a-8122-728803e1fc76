@import "../common.scss";
.login {
  flex: 1;
  @include flex(column, center, stretch);
  min-width: 980px;
  width: 100%;
  height: 100%;
  background: #ffffff;
  .login-main {
    flex: 1;
    @include flex(column, center, stretch);
    padding-bottom: 5vw;
    .login-main-content {
      flex: 1;
      @include flex(row, space-between, center);
      padding: 0 6vh;
      width: 100%;
      max-width: 100rem;
      align-self: center;
      background: url("../assets/image/login-background.png") no-repeat;
      background-position: center left;
      .login-main-adv {
        @include flex(column, flex-start, flex-start);
        .login-main-adv-introduce {
          font-size: 3rem;
          line-height: 4.2rem;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          color: #000000;
        }
      }

      .login-sale {
        margin-top: 40px;
        padding: 8px 16px;
        font-size: 1.4rem;
        line-height: 2rem;
        border-radius: 6px;
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #ffffff;
        background: url("../assets/image/adv-bg.svg") no-repeat;
        background-size: cover;
        background-position: center;
        .icon {
          margin: 0 8px;
        }
      }

      .small-txt {
        width: 42rem;
        font-size: 2.6rem;
        line-height: 3.6rem;
      }

      .checked-text {
        display: flex;
        flex-wrap: wrap;
        font-size: 0.88rem;
        line-height: 1.73rem;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #bbbbbb;
        letter-spacing: 0;
        a {
          color: #006ef0;
        }
      }
      .login-form {
        box-sizing: border-box;
        width: 22.41rem;

        .login-title {
          display: flex;
          letter-spacing: 0;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          color: #000000;
          img {
            width: 4.61rem;
            height: 3.23rem;
          }
          p {
            padding-left: 10.5px;
            font-size: 1.8rem;
            line-height: 2.7rem;
            align-items: center;
            display: flex;
          }
        }

        .login-form-item {
          margin: 18px 0;
          .el-select {
            width: 100%;
            font-size: 1rem;
            padding-top: 28px;
            line-height: 1.73rem;
            font-weight: 400;
            color: #000000;
          }

          .el-input__inner {
            width: 356.4px;
            height: 54px;
            margin-top: 12px;
            border-radius: 4.8px;
            background: #ffffff;
            border: 1.2px solid #dddddd;
          }

          .input-with-select {
            margin-top: 14px;
            input {
              height: 40px;
            }
          }
          .el-input-group__append {
            .code-box {
              color: #006eff;
            }
          }
          .login-form-item-disabled {
            cursor: pointer;
            background: #f4f5f9;
            border: 1.2px solid #dddddd;
            color: #111111;
            letter-spacing: 0;
            border-radius: 4.8px;
            width: 100%;
            margin-top: 28px;
            padding: 14px 11px;
            box-sizing: border-box;
            font-size: 1rem;
            line-height: 1.2rem;

            label {
              cursor: pointer;
              color: #999999;
              padding-right: 16px;
            }
          }
        }
        .login-form-footer {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          text-decoration: none;
          a {
            color: #006ef0;
          }
        }

        .login-btn {
          flex: 1;
          @include flex(column, center, stretch);
          .btn {
            width: 100%;
            flex: 1;
            height: 3rem;
            font-size: 1.25rem;
            font-weight: 400;
            letter-spacing: 0;
            border: 1px solid #006eff;
            border-radius: 5px;
            color: #006eff;
            margin-bottom: 10px;
            background-color: transparent;
            cursor: pointer;

            &:disabled {
              opacity: 0.3;
            }

            &-primary {
              background: #006eff;
              color: #ffffff;
            }
          }
        }
      }
    }

    .login-main-middle {
      height: 130px;
      width: 100%;
      max-width: 100rem;
      align-self: center;
      padding: 0 1.6rem 20px;
      box-sizing: border-box;
      display: flex;

      .login-main-middle-box {
        flex: 1;
        display: flex;
      }
    }
    .login-main-footer {
      box-sizing: border-box;
      padding: 0 1.6rem;
      width: 100%;
      max-width: 100rem;
      display: flex;
      align-items: center;
      align-self: center;
      background: rgba(231, 242, 255, 0.4);
      .mask {
        flex: 0 0 25%;
        padding: 1.6rem 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        .mask-top {
          word-break: break-all;
          white-space: nowrap;
          font-size: 3rem;
          height: 4.19rem;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          color: #006eff;
          letter-spacing: 0;
        }
        .mask-under {
          opacity: 0.49;
          font-weight: 400;
          font-size: 1.2rem;
          font-family: PingFangSC-Regular;
          color: #000;
          letter-spacing: 0;
          height: 62px;
          text-align: center;
        }
      }
    }
  }
}
