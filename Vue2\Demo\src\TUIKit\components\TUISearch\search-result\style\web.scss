.tui-search-result {
  overflow: hidden;
  flex: 1;
  display: flex;
  width: 100%;
  flex-direction: row;
  box-sizing: border-box;

  &-with-border {
    border-top: 1px solid rgba(0,0,0,0.1);
  }

  &-detail {
    width: 360px;
    overflow-y: hidden;
    border-left: 1px solid rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    flex: 1;

    .tui-search-message-header {
      padding: 10px;
      display: flex;
      flex-direction: row;
      place-content: space-between space-between;
      font-size: 14px;
      align-items: center;

      .header-content {
        display: flex;
        flex-flow: row nowrap;
        flex: 1;
        overflow: hidden;
        color: #666;
        white-space: nowrap;

        .header-content-count {
          width: fit-content;
          white-space: nowrap;
        }

        .header-content-keyword {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .header-content-type {
          width: 110px;
          white-space: nowrap;
        }

        .normal {
          color: #666;
        }

        .highlight {
          color: #007aff;
        }
      }

      .header-enter {
        margin-left: 10px;
        width: 70px;
        color: #666;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }

    .tui-search-message-list {
      overflow-y: auto;

      .list-item {
        width: 100%;
        flex: 1;
        overflow: hidden;
      }

      .list-group {
        &.list-group-image {
          display: flex;
          flex-flow: row wrap;

          .list-group-item {
            width: 111px;
            height: 111px;
          }
        }

        .list-group-date {
          width: 100%;
          box-sizing: border-box;
          font-family: "PingFang SC", sans-serif;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          letter-spacing: 0;
          text-align: left;
          padding: 10px 10px 2px;
        }
      }

      .more {
        display: flex;
        flex-direction: row;
        font-size: 14px;
        padding: 8px 0;
        justify-content: center;
        align-items: center;
        user-select: none;
        cursor: pointer;

        .more-text {
          padding-left: 8px;
          font-size: 12px;
          color: #007aff;
          user-select: none;
        }
      }
    }
  }

  &-main {
    width: 350px;
    padding: 10px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    flex: 1;

    .tui-search-result-list {
      &-item {
        .header {
          font-size: 14px;
          padding: 4px 0;
        }

        .list {
          display: flex;
          flex-direction: column;

          .list-item {
            cursor: pointer;
          }

          .list-item-selected {
            background: #f2f2f2;
            border-radius: 5px;
          }
        }

        .more {
          display: flex;
          flex-direction: row;
          font-size: 14px;

          // padding: 8px 0;
          padding-top: 10px;
          user-select: none;
          cursor: pointer;

          .more-text {
            padding-left: 8px;
            font-size: 12px;
            color: #007aff;
            user-select: none;
          }
        }
      }
    }
  }

  .tui-search-result-in-conversation {
    border: none;
  }
}
