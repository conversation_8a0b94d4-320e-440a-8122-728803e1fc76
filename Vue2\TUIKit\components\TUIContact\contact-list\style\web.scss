.tui-contact-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  list-style: none;

  &-item {
    display: flex;
    flex-direction: column;

    &-header {
      display: flex;
      flex-direction: row;
      font-size: 14px;
      cursor: pointer;
      user-select: none;
      padding: 10px 15px;
      justify-content: space-between;

      &-left {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      &-right {
        display: flex;
        justify-content: center;
        align-items: center;

        &-unread {
          display: flex;
          min-width: 10px;
          width: fit-content;
          padding: 0 2.5px;
          height: 15px;
          font-size: 10px;
          text-align: center;
          line-height: 15px;
          border-radius: 7.5px;
          background: red;
          align-items: center;
          justify-content: center;
          color: #fff;
        }
      }
    }

    &-main {
      padding: 0 15px !important;

      &.hidden{
        display: none;
      }

      &-item {
        padding: 5px 0;
      }
    }
  }
}

.tui-contact-search-list {
  padding: 0 15px !important;

  &-title {
    font-size: 14px;
    color: #999;
    border-bottom: 1px solid #f4f5f9;
  }

  &-item {
    padding: 5px 0;
  }

  &-default {
    padding: 20px;
    text-align: center;
    font-size: 14px;
    color: #999;
  }
}
