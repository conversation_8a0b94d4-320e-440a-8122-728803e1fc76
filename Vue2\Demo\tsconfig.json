{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "moduleResolution": "node", "noEmitOnError": false, "experimentalDecorators": true, "resolveJsonModule": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "sourceMap": true, "strictFunctionTypes": false, "baseUrl": ".", "types": ["node", "webpack-env", "vue/types", "vue-router/types"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "vueCompilerOptions": {"target": 2.7}, "files": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules", "src/main.ts"]}