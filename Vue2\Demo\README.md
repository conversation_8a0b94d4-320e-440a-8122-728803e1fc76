<p align="center">
  <span>English / </span>
  <a href="https://github.com/TencentCloud/chat-uikit-vue/tree/main/Vue2/Demo/README_ZH.md">简体中文</a>
</p>

# Quick Run Demo

## About chat-uikit-vue

[chat-uikit-vue](https://www.npmjs.com/package/@tencentcloud/chat-uikit-vue) is a Vue UI component library based on Tencent Cloud Chat SDK. It provides universally used UI components that include Conversation, Chat, and Group components. Leveraging these meticulously crafted UI components, you can quickly construct an elegant, reliable, and scalable Chat application.

> [!IMPORTANT]
> In respect for the copyright of the emoji design, the Chat Demo/TUIKit project does not include the cutouts of large emoji elements. Please replace them with your own designed or copyrighted emoji packs before the official launch for commercial use. The default small yellow face emoji pack is copyrighted by Tencent Cloud and can be authorized for a fee. If you wish to obtain authorization, please submit a ticket to contact us.
> 
> submit a ticket url：https://console.tencentcloud.com/workorder/category?level1_id=29&level2_id=40&source=14&data_title=Chat&step=1

![image](https://github.com/TencentCloud/chat-uikit-vue/assets/57951148/7bd24604-1e5e-4541-8992-245dccbbc810)
![image](https://github.com/TencentCloud/chat-uikit-vue/assets/57951148/40ae2f49-39ae-432d-8d1b-5b46414bc3b4)

#### 👉🏻 [Try Online Demo](https://web.sdk.qcloud.com/im/demo/intl/index.html?scene=social)

### Quick Run Demo
#### Step 1：Install Demo Source Code

```shell
git clone https://github.com/TencentCloud/chat-uikit-vue.git

cd chat-uikit-vue/Vue2/Demo

npm i --legacy-peer-deps
```
#### Step 2：Secure SDKAppID and secretKey
Set the relevant parameters SDKAppID and secretKey in the example code of the main.ts / main.js file:
SDKAppID and SecretKey can be accessed by the [Chat Console](https://console.trtc.io/app):
![image](https://github.com/TencentCloud/chat-uikit-react/assets/57951148/09c7c16b-5ff8-4b2d-bb1b-b0bf72a754ed)

#### Step 3：Run Demo
```javascript
npm run dev
```

## Documentation
- [Home page](https://trtc.io/document/50061?platform=web&product=chat)
- [@tencentcloud/chat-uikit-vue npm](https://www.npmjs.com/package/@tencentcloud/chat-uikit-vue)
- [Chat SDK](https://trtc.io/document/34309?platform=web&product=chat)

## Contact Us
Join a Tencent Cloud Chat developer group for Reliable technical support & Product details & Constant exchange of ideas.
- Telegram group (EN): [join](https://t.me/+1doS9AUBmndhNGNl)
- WhatsApp group (EN): [join](https://chat.whatsapp.com/Gfbxk7rQBqc8Rz4pzzP27A)
- Telegram group (ZH): [join](https://t.me/tencent_imsdk)
- WhatsApp group (ZH): [join](https://chat.whatsapp.com/IVa11ZkVmKTEwSWsAzSyik)



