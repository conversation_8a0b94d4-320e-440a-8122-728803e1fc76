{"name": "sample-web-vue2", "version": "2.4.3", "description": "", "keywords": [], "author": "", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@tencentcloud/call-uikit-vue2": "3.3.9", "@tencentcloud/chat-uikit-engine": "latest", "@tencentcloud/chat-uikit-vue": "2.4.3", "@tencentcloud/tui-core": "latest", "@tencentcloud/universal-api": "latest", "@tencentcloud/tui-customer-service-plugin": "latest", "@tencentcloud/tui-emoji-plugin": "latest", "@tiptap/core": "2.0.0-beta.220", "@tiptap/extension-hard-break": "2.0.0-beta.220", "@tiptap/extension-document": "2.0.0-beta.220", "@tiptap/extension-image": "2.0.0-beta.220", "@tiptap/extension-mention": "2.0.0-beta.220", "@tiptap/extension-paragraph": "2.0.0-beta.220", "@tiptap/extension-placeholder": "2.0.0-beta.220", "@tiptap/extension-text": "2.0.0-beta.220", "@tiptap/pm": "2.0.0-beta.220", "@tiptap/suggestion": "2.0.0-beta.220", "@types/webpack-env": "^1.18.0", "axios": "^0.26.1", "dayjs": "^1.11.10", "element-ui": "^2.15.13", "highlight.js": "^11.10.0", "dompurify": "^3.2.1", "marked": "5.1.2", "marked-highlight": "^2.2.1", "lodash": "^4.17.21", "qs": "^6.10.3", "vue": "2.7.9", "vue-class-component": "^7.2.3", "vue-property-decorator": "^9.1.2", "vue-router": "3.6.5", "vue-template-compiler": "2.7.9", "vuex": "3.5.0", "webpack-env": "^0.8.0"}, "devDependencies": {"@stylistic/eslint-plugin": "^1.6.2", "@types/lodash": "^4.14.202", "@types/node": "18.19.45", "@types/qs": "^6.9.7", "@types/marked": "^5.0.2", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~4.0.0", "@vue/cli-plugin-eslint": "~4.0.0", "@vue/cli-plugin-router": "~4.0.0", "@vue/cli-plugin-typescript": "~4.0.0", "@vue/cli-plugin-vuex": "~4.0.0", "@vue/cli-service": "~4.0.0", "@vue/eslint-config-typescript": "^12.0.0", "babel-plugin-component": "^1.1.1", "core-js": "^3.8.3", "eslint-plugin-vue": "^9.21.1", "eslint": "^8.56.0", "node": "^19.8.1", "postcss-html": "^1.6.0", "sass-loader": "^12.0.0", "sass": "^1.32.7", "stylelint": "^16.2.1", "stylelint-config-standard-scss": "^13.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-config-standard": "^36.0.0", "typescript": "~4.5.5", "vue-template-compiler": "2.7.9"}}