.evaluate-h5 {
  position: static;
  width: 100%;
  height: fit-content;
  border-radius: 0;
  background: #fff;
  padding: 23px !important;
  box-sizing: border-box;

  &-header {
    display: flex;
    justify-content: space-between;

    &-content {
      font-size: 18px;
    }

    &-close {
      font-size: 18px;
      line-height: 27px;
      font-weight: 400;
      color: #3370ff;
    }
  }

  &-content {
    order: 1;

    &-list {
      &-item {
        width: 40px;
        height: 24px;
        text-align: center;
        cursor: auto;
        font-size: 12px;
      }
    }

    &-text {
      font-size: 16px;
      width: 100%;
    }

    &-button {
      width: 100%;
      display: flex;

      .btn {
        flex: 1;
        padding: 14px 0;
        font-size: 18px;
        cursor: auto;
      }
    }
  }

  &-adv {
    font-size: 14px;
    font-weight: normal;
    text-align: left;
    color: #000;
  }
}
