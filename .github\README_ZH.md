<h1 align="center">chat-uikit-vue</h1>

<br>

<p align="center">
  Globally interconnected In-App Chat, user profile and relationship chains and online/offline push.
</p>

<br>

<p align="center">
  <a href="https://github.com/TencentCloud/chat-uikit-vue/blob/main/.github/README.md">English</a>
  <span> / 简体中文</span>
</p>

<br>

<img width="1015" alt="page02" src="https://user-images.githubusercontent.com/57951148/192585298-c79960ed-a6a9-4927-89b9-31c1b3f68740.png">
<img width="2072" alt="page00" src="https://user-images.githubusercontent.com/57951148/192585375-6260280f-4a67-4b64-a908-efcedee1c253.png">

## 关于 chat-uikit-vue

chat-uikit-vue 是基于腾讯云 Web Chat SDK 的一款 VUE UI 组件库，它提供了一些通用的 UI 组件，包含会话、聊天、音视频通话、关系链、资料、群组等功能。基于 UI 组件您可以像搭积木一样快速搭建起自己的业务逻辑。
chat-uikit-vue 中的组件在实现 UI 功能的同时，会调用 Chat SDK 相应的接口实现 IM 相关逻辑和数据的处理，因而开发者在使用 chat-uikit-vue 时只需关注自身业务或个性化扩展即可。

> [!IMPORTANT]
> 为尊重表情设计版权，IM Demo/TUIKit 工程中不包含大表情元素切图，正式上线商用前请您替换为自己设计或拥有版权的其他表情包。默认的小黄脸表情包版权归腾讯云所有，可有偿授权使用，如您希望获得授权可 提交工单 联系我们。
>
> 提交工单链接：https://console.cloud.tencent.com/workorder/category?level1_id=29&level2_id=40&source=14&data_title=%E5%8D%B3%E6%97%B6%E9%80%9A%E4%BF%A1%20IM&step=1

## 在线 Demo 体验

👉🏻 [Web & H5 Demo](https://web.sdk.qcloud.com/im/demo/latest/index.html#/)

## 发送您的第一条消息

### Vue3 版本
#### 集成 TUIKit
请点击 [@tencentcloud/chat-uikit-vue](https://cloud.tencent.com/document/product/269/68493) 进行快速集成 Vue3 UIKit。
#### 快速跑通 Demo
请点击 [快速跑通 Demo(Vue3)](https://github.com/TencentCloud/chat-uikit-vue/blob/main/Vue3/Demo/README_ZH.md) 体验快速跑通 Vue3 UIKit Demo。

### Vue2 版本
#### 集成 TUIKit
请点击 [@tencentcloud/chat-uikit-vue](https://cloud.tencent.com/document/product/269/68493) 进行快速集成 Vue2 UIKit。
#### 快速跑通 Demo
请点击 [快速跑通 Demo(Vue2)](https://github.com/TencentCloud/chat-uikit-vue/blob/main/Vue2/Demo/README_ZH.md) 体验快速跑通 Vue2 UIKit Demo。


## 相关文档
- [快速跑通 Demo(Vue2)](https://github.com/TencentCloud/chat-uikit-vue/tree/main/Vue2/Demo)
- [快速跑通 Demo(Vue3)](https://github.com/TencentCloud/chat-uikit-vue/tree/main/Vue3/Demo)
- [@tencentcloud/chat-uikit-vue npm仓库(Vue2 & Vue3)](https://www.npmjs.com/package/@tencentcloud/chat-uikit-vue)
- [SDK API手册](https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html)
- [SDK 更新日志](https://cloud.tencent.com/document/product/269/38492)

## 技术咨询
[点此进入 IM 社群](https://zhiliao.qq.com/)，享有专业工程师的支持，解决您的难题。

