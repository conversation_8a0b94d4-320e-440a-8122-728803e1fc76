<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="48px" height="48px" viewBox="0 0 48 48" version="1.1">
  <title>编组 8</title>
  <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="消息页" transform="translate(-351.000000, -1405.000000)" fill="#B0B4B8">
      <g id="编组-15" transform="translate(0.000000, 1378.000000)">
        <g id="编组-12备份-5" transform="translate(32.000000, 12.000000)">
          <g id="编组-6" transform="translate(229.000000, 0.000000)">
            <g id="编组-2" transform="translate(84.000000, 15.000000)">
              <g id="编组-8" transform="translate(6.000000, 0.000000)">
                <path d="M25,26 C29.418278,26 33,29.581722 33,34 L33,40 C33,41.1045695 32.1045695,42 31,42 L5,42 C3.8954305,42 3,41.1045695 3,40 L3,34 C3,29.581722 6.581722,26 11,26 L25,26 Z M43.9994591,26 C44.5517438,26 44.9994591,26.4477153 44.9994591,27 L44.9994591,29 C44.9994591,29.5522847 44.5517438,30 43.9994591,30 L36.9994591,30 C36.4471743,30 35.9994591,29.5522847 35.9994591,29 L35.9994591,27 C35.9994591,26.4477153 36.4471743,26 36.9994591,26 L43.9994591,26 Z M18.0020419,6 C22.9737572,6 27.0040839,10.0294125 27.0040839,15 C27.0040839,19.9705875 22.9737572,24 18.0020419,24 C13.0303267,24 9,19.9705875 9,15 C9,10.0294125 13.0303267,6 18.0020419,6 Z M43.9994591,18 C44.5517438,18 44.9994591,18.4477153 44.9994591,19 L44.9994591,21 C44.9994591,21.5522847 44.5517438,22 43.9994591,22 L31.9994591,22 C31.4471743,22 30.9994591,21.5522847 30.9994591,21 L30.9994591,19 C30.9994591,18.4477153 31.4471743,18 31.9994591,18 L43.9994591,18 Z" id="形状结合"></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
