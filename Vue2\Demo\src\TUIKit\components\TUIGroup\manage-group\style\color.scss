.manage {
  background: #FFF;
  box-shadow: 0 1px 10px 0 rgba(2, 16, 43, 0.15);

  &-header {
    border-bottom: 1px solid #E8E8E9;

    .manage-header-content {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: #000;
    }
  }

  .main {
    .footer {
      .list-item {
        font-weight: 400;
        color: #dc2113;
        border-bottom: 1px solid #E8E8E9;
      }
    }
  }
}

.input {
  border: 1px solid #E8E8E9;
  font-weight: 400;
  color: #000;
  opacity: 0.6;
}

.avatar {
  background: #F4F5F9;
  color: #000;
}

.space-top {
  border-top: 10px solid #F4F5F9;
}

.btn {
  background: #3370FF;
  border: 0 solid #2F80ED;
  color: #FFF;

  &-cancel {
    background: #FFF;
    border: 1px solid #DDD;
    color: #828282;
  }
}

.slider {
  &-box {
    background: #E1E1E3;
  }

  &-block {
    background: #FFF;
    border: 0 solid rgba(0, 0, 0, 0.85);
    box-shadow: 0 2px 4px 0 #D1D1D1;
  }
}
