.group {
  background: #FFF;

  &-list {
    &-item {
      background: #FFF;

      label {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #333;
      }
    }

    input {
      border: 1px solid rgba(131, 137, 153, 0.40);
      font-weight: 400;
      color: #333;
    }

    &-edit {
      background: #FFF;
    }
  }

  &-profile-footer {
    background: #FFF;
  }

  &-h5 {
    background: #F7F8FA;

    &-list-item-introduction {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: #888;

      a {
        color: #006EFF;
      }
    }
  }
}

.select {
  flex: 1;

  a {
    color: #006EFF;
  }

  &-item {
    border: 1px solid rgba(131, 137, 153, 0.40);

    &-header {
      .left {
        font-weight: 500;
        color: #333;
      }
    }

    &-detail {
      color: #4F4F4F;
    }
  }

  .selected {
    border: 1px solid #006EFF;
  }
}

header {
  background: #FFF;

  h1 {
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #000;
    letter-spacing: 0;
  }
}

.btn-default {
  background: #FFF;
  border: 1px solid #DDD;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: #828282;
}

.btn-submit {
  background: #3370FF;
  border: 0 solid #2F80ED;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: #FFF;
  letter-spacing: 0;

  &:disabled {
    background: #e8e8e9;
    border: 1px solid #DDD;
    color: #FFF;
  }
}
